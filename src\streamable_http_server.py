"""
Streamable HTTP MCP Server

This is the main server implementation that combines all components of the
Streamable HTTP MCP architecture for real-time screen capture streaming.

Features:
- Streamable HTTP transport layer
- MCP protocol compliance
- Real-time screen capture streaming
- HTTP/2 optimization
- Chunked transfer encoding
- Adaptive quality control
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

import structlog

from .config import MCPServerConfig, get_config
from .transport import StreamableHTTPTransport
from .protocol import StreamableHTTPMCPAdapter
from .streaming import HTTPScreenStreamer, StreamQuality
from .screen_capture import ScreenCapture

logger = structlog.get_logger(__name__)


class StreamableHTTPMCPServer:
    """Main Streamable HTTP MCP Server."""
    
    def __init__(self, config: Optional[MCPServerConfig] = None):
        """Initialize Streamable HTTP MCP Server."""
        self.config = config or get_config()
        
        # Core components
        self.transport = StreamableHTTPTransport(
            host=self.config.host,
            port=self.config.port
        )
        self.adapter = StreamableHTTPMCPAdapter(self.config, self.transport)
        self.streamer = HTTPScreenStreamer(self.config)
        self.screen_capture = ScreenCapture(self.config)
        
        # Server state
        self.running = False
        self.start_time: Optional[float] = None
        
        # Setup MCP tools and resources
        self._setup_mcp_handlers()
        
        logger.info(
            "Streamable HTTP MCP Server initialized",
            server_name=self.config.server_name,
            version=self.config.server_version,
            host=self.config.host,
            port=self.config.port
        )
    
    def _setup_mcp_handlers(self):
        """Setup MCP tools, resources, and stream handlers."""
        
        # Register tools
        self.adapter.register_tool(
            "start_stream",
            self._tool_start_stream,
            {
                "description": "Start real-time screen streaming",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "quality": {
                            "type": "string",
                            "enum": ["low", "medium", "high", "adaptive"],
                            "description": "Stream quality level"
                        },
                        "fps": {
                            "type": "integer",
                            "minimum": 1,
                            "maximum": 60,
                            "description": "Frames per second"
                        }
                    }
                }
            }
        )
        
        self.adapter.register_tool(
            "stop_stream",
            self._tool_stop_stream,
            {
                "description": "Stop screen streaming",
                "inputSchema": {"type": "object", "properties": {}}
            }
        )
        
        self.adapter.register_tool(
            "capture_screenshot",
            self._tool_capture_screenshot,
            {
                "description": "Capture a single screenshot",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "format": {
                            "type": "string",
                            "enum": ["jpeg", "png", "webp"],
                            "description": "Image format"
                        },
                        "quality": {
                            "type": "integer",
                            "minimum": 1,
                            "maximum": 100,
                            "description": "Image quality (for JPEG/WebP)"
                        }
                    }
                }
            }
        )
        
        self.adapter.register_tool(
            "get_stream_status",
            self._tool_get_stream_status,
            {
                "description": "Get current streaming status and statistics",
                "inputSchema": {"type": "object", "properties": {}}
            }
        )
        
        self.adapter.register_tool(
            "list_monitors",
            self._tool_list_monitors,
            {
                "description": "List available monitors",
                "inputSchema": {"type": "object", "properties": {}}
            }
        )
        
        self.adapter.register_tool(
            "update_config",
            self._tool_update_config,
            {
                "description": "Update streaming configuration",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "fps": {"type": "integer", "minimum": 1, "maximum": 60},
                        "quality": {"type": "integer", "minimum": 1, "maximum": 100},
                        "compression": {"type": "string", "enum": ["none", "gzip", "zstd"]},
                        "monitor_index": {"type": "integer", "minimum": 0}
                    }
                }
            }
        )
        
        # Register resources
        self.adapter.register_resource(
            "screen://current",
            self._resource_current_screen,
            {
                "name": "Current Screen",
                "description": "Current screen capture as base64 image",
                "mimeType": f"image/{self.config.image_format.value}"
            }
        )
        
        self.adapter.register_resource(
            "stream://status",
            self._resource_stream_status,
            {
                "name": "Stream Status",
                "description": "Current streaming status and statistics",
                "mimeType": "application/json"
            }
        )
        
        self.adapter.register_resource(
            "info://monitors",
            self._resource_monitors_info,
            {
                "name": "Monitors Information",
                "description": "Available monitors information",
                "mimeType": "application/json"
            }
        )
        
        # Register stream handlers
        self.adapter.register_stream_handler("screen", self._handle_screen_stream)
        self.transport.register_stream_handler("screen", self._transport_screen_stream)
    
    # Tool handlers
    async def _tool_start_stream(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Start streaming tool."""
        try:
            quality = args.get("quality", "adaptive")
            fps = args.get("fps", self.config.fps)
            
            # Update FPS if provided
            if fps != self.config.fps:
                self.config.fps = fps
            
            # Set quality
            if quality in ["low", "medium", "high", "adaptive"]:
                self.streamer.set_quality(StreamQuality(quality))
            
            # Start streaming (this will be handled by the stream handler)
            return {
                "status": "started",
                "quality": quality,
                "fps": fps,
                "message": "Screen streaming started successfully"
            }
            
        except Exception as e:
            logger.error("Failed to start stream", error=str(e))
            return {
                "status": "error",
                "message": f"Failed to start streaming: {str(e)}"
            }
    
    async def _tool_stop_stream(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Stop streaming tool."""
        try:
            self.streamer.stop_streaming()
            return {
                "status": "stopped",
                "message": "Screen streaming stopped successfully"
            }
        except Exception as e:
            logger.error("Failed to stop stream", error=str(e))
            return {
                "status": "error",
                "message": f"Failed to stop streaming: {str(e)}"
            }
    
    async def _tool_capture_screenshot(self, args: Dict[str, Any]) -> bytes:
        """Capture screenshot tool."""
        try:
            frame_data = self.screen_capture.capture_screen()
            if frame_data:
                return frame_data
            else:
                raise Exception("Failed to capture screen")
        except Exception as e:
            logger.error("Screenshot capture failed", error=str(e))
            raise
    
    async def _tool_get_stream_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Get stream status tool."""
        return self.streamer.get_stats()
    
    async def _tool_list_monitors(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """List monitors tool."""
        monitors = []
        for i, monitor in enumerate(self.screen_capture.monitors[1:], 1):  # Skip "All in One"
            monitors.append({
                "index": i - 1,
                "name": f"Monitor {i}",
                "width": monitor["width"],
                "height": monitor["height"],
                "left": monitor["left"],
                "top": monitor["top"]
            })
        
        return {
            "monitors": monitors,
            "current": self.config.monitor_index,
            "total": len(monitors)
        }
    
    async def _tool_update_config(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Update configuration tool."""
        updated_fields = []
        
        if "fps" in args:
            self.config.fps = args["fps"]
            updated_fields.append("fps")
        
        if "quality" in args:
            self.config.jpeg_quality = args["quality"]
            updated_fields.append("quality")
        
        if "compression" in args:
            from .config import CompressionType
            self.config.compression = CompressionType(args["compression"])
            updated_fields.append("compression")
        
        if "monitor_index" in args:
            self.config.monitor_index = args["monitor_index"]
            updated_fields.append("monitor_index")
        
        return {
            "status": "updated",
            "fields": updated_fields,
            "message": f"Updated configuration: {', '.join(updated_fields)}"
        }
    
    # Resource handlers
    async def _resource_current_screen(self) -> bytes:
        """Current screen resource."""
        frame_data = self.screen_capture.capture_screen()
        if frame_data:
            return frame_data
        else:
            raise Exception("Failed to capture screen")
    
    async def _resource_stream_status(self) -> Dict[str, Any]:
        """Stream status resource."""
        return self.streamer.get_stats()
    
    async def _resource_monitors_info(self) -> Dict[str, Any]:
        """Monitors info resource."""
        return await self._tool_list_monitors({})
    
    # Stream handlers
    async def _handle_screen_stream(self, action: str, params: Dict[str, Any]) -> Any:
        """Handle screen stream requests."""
        if action == "start":
            # Stream will be handled by transport layer
            return "started"
        elif action == "stop":
            self.streamer.stop_streaming()
            return "stopped"
        elif action == "status":
            return self.streamer.get_stats()
        else:
            return f"Unknown action: {action}"
    
    async def _transport_screen_stream(self):
        """Transport layer screen stream generator."""
        async for chunk in self.streamer.start_streaming():
            yield chunk
    
    async def start(self):
        """Start the Streamable HTTP MCP Server."""
        try:
            self.running = True
            self.start_time = time.time()
            
            logger.info("Starting Streamable HTTP MCP Server...")
            
            # Start the adapter (which starts the transport)
            await self.adapter.start()
            
            logger.info(
                "Streamable HTTP MCP Server started successfully",
                host=self.config.host,
                port=self.config.port,
                endpoints=[
                    f"http://{self.config.host}:{self.config.port}/mcp/message",
                    f"http://{self.config.host}:{self.config.port}/mcp/stream",
                    f"http://{self.config.host}:{self.config.port}/stream/screen",
                    f"http://{self.config.host}:{self.config.port}/health"
                ]
            )
            
        except Exception as e:
            self.running = False
            logger.error("Failed to start server", error=str(e), exc_info=True)
            raise
    
    async def stop(self):
        """Stop the Streamable HTTP MCP Server."""
        try:
            logger.info("Stopping Streamable HTTP MCP Server...")
            
            self.running = False
            
            # Stop streaming
            self.streamer.stop_streaming()
            
            # Stop adapter
            await self.adapter.stop()
            
            logger.info("Streamable HTTP MCP Server stopped successfully")
            
        except Exception as e:
            logger.error("Error stopping server", error=str(e))
    
    def get_server_info(self) -> Dict[str, Any]:
        """Get server information."""
        uptime = time.time() - self.start_time if self.start_time else 0
        
        return {
            "name": self.config.server_name,
            "version": self.config.server_version,
            "transport": "streamable_http",
            "running": self.running,
            "uptime": uptime,
            "host": self.config.host,
            "port": self.config.port,
            "endpoints": {
                "mcp_message": f"http://{self.config.host}:{self.config.port}/mcp/message",
                "mcp_stream": f"http://{self.config.host}:{self.config.port}/mcp/stream",
                "screen_stream": f"http://{self.config.host}:{self.config.port}/stream/screen",
                "events": f"http://{self.config.host}:{self.config.port}/stream/events",
                "health": f"http://{self.config.host}:{self.config.port}/health",
                "status": f"http://{self.config.host}:{self.config.port}/status"
            },
            "config": self.config.dict()
        }
