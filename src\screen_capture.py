"""
Cross-platform screen capture module using mss library.
Provides high-performance screen capturing with various optimization options.
"""

import asyncio
import time
import threading
from typing import Optional, Dict, Any, Tuple, List
from dataclasses import dataclass
from io import BytesIO

import mss
from PIL import Image
import structlog

from .config import MCPServerConfig, ImageFormat, get_config

logger = structlog.get_logger(__name__)


@dataclass
class CaptureStats:
    """Statistics for screen capture performance."""
    total_captures: int = 0
    total_time: float = 0.0
    avg_fps: float = 0.0
    last_capture_time: float = 0.0
    errors: int = 0


class ScreenCapture:
    """High-performance cross-platform screen capture."""
    
    def __init__(self, config: Optional[MCPServerConfig] = None):
        """Initialize screen capture with configuration."""
        self.config = config or get_config()
        self.sct = mss.mss()
        self.stats = CaptureStats()
        self._running = False
        self._capture_thread: Optional[threading.Thread] = None
        self._frame_buffer: List[bytes] = []
        self._buffer_lock = threading.Lock()
        
        # Get monitor information
        self.monitors = self.sct.monitors
        self.monitor_count = len(self.monitors) - 1  # Exclude "All in One" monitor
        
        logger.info(
            "Screen capture initialized",
            monitor_count=self.monitor_count,
            monitors=self.monitors[1:],  # Skip "All in One"
            config=self.config.dict()
        )
    
    def get_monitor_info(self) -> List[Dict[str, Any]]:
        """Get information about available monitors."""
        monitor_info = []
        for i, monitor in enumerate(self.monitors[1:], 0):  # Skip "All in One"
            monitor_info.append({
                "index": i,
                "x": monitor["left"],
                "y": monitor["top"],
                "width": monitor["width"],
                "height": monitor["height"],
                "primary": i == 0
            })
        return monitor_info
    
    def _get_capture_region(self) -> Dict[str, int]:
        """Get the capture region based on configuration."""
        if self.config.capture_region:
            return self.config.capture_region
        
        # Use specified monitor or primary monitor
        monitor_index = min(self.config.monitor_index, self.monitor_count - 1)
        monitor = self.monitors[monitor_index + 1]  # +1 to skip "All in One"
        
        return {
            "left": monitor["left"],
            "top": monitor["top"],
            "width": monitor["width"],
            "height": monitor["height"]
        }
    
    def capture_screen(self) -> Optional[bytes]:
        """Capture screen and return as bytes."""
        try:
            start_time = time.time()
            
            # Get capture region
            region = self._get_capture_region()
            
            # Capture screen
            screenshot = self.sct.grab(region)
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            # Resize if needed
            if self.config.max_width or self.config.max_height:
                img = self._resize_image(img)
            
            # Convert to bytes
            img_bytes = self._image_to_bytes(img)
            
            # Update statistics
            capture_time = time.time() - start_time
            self.stats.total_captures += 1
            self.stats.total_time += capture_time
            self.stats.last_capture_time = capture_time
            self.stats.avg_fps = self.stats.total_captures / self.stats.total_time if self.stats.total_time > 0 else 0
            
            logger.debug(
                "Screen captured",
                size=len(img_bytes),
                capture_time=capture_time,
                fps=1.0 / capture_time if capture_time > 0 else 0
            )
            
            return img_bytes
            
        except Exception as e:
            self.stats.errors += 1
            logger.error("Screen capture failed", error=str(e), exc_info=True)
            return None
    
    def _resize_image(self, img: Image.Image) -> Image.Image:
        """Resize image while maintaining aspect ratio."""
        original_width, original_height = img.size
        max_width = self.config.max_width or original_width
        max_height = self.config.max_height or original_height
        
        # Calculate scaling factor
        width_ratio = max_width / original_width
        height_ratio = max_height / original_height
        scale_factor = min(width_ratio, height_ratio, 1.0)  # Don't upscale
        
        if scale_factor < 1.0:
            new_width = int(original_width * scale_factor)
            new_height = int(original_height * scale_factor)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            logger.debug(
                "Image resized",
                original_size=(original_width, original_height),
                new_size=(new_width, new_height),
                scale_factor=scale_factor
            )
        
        return img
    
    def _image_to_bytes(self, img: Image.Image) -> bytes:
        """Convert PIL Image to bytes."""
        buffer = BytesIO()
        
        if self.config.image_format == ImageFormat.JPEG:
            img.save(buffer, format="JPEG", quality=self.config.jpeg_quality, optimize=True)
        elif self.config.image_format == ImageFormat.PNG:
            img.save(buffer, format="PNG", optimize=True)
        elif self.config.image_format == ImageFormat.WEBP:
            img.save(buffer, format="WEBP", quality=self.config.jpeg_quality, optimize=True)
        else:
            # Default to JPEG
            img.save(buffer, format="JPEG", quality=self.config.jpeg_quality, optimize=True)
        
        return buffer.getvalue()
    
    async def start_continuous_capture(self) -> None:
        """Start continuous screen capture in background thread."""
        if self._running:
            logger.warning("Screen capture already running")
            return
        
        self._running = True
        
        if self.config.enable_threading:
            self._capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
            self._capture_thread.start()
            logger.info("Started threaded screen capture")
        else:
            # Run in current event loop
            asyncio.create_task(self._async_capture_loop())
            logger.info("Started async screen capture")
    
    def _capture_loop(self) -> None:
        """Continuous capture loop for threading mode."""
        frame_interval = 1.0 / self.config.fps
        
        while self._running:
            start_time = time.time()
            
            # Capture frame
            frame_data = self.capture_screen()
            if frame_data:
                # Add to buffer
                with self._buffer_lock:
                    self._frame_buffer.append(frame_data)
                    # Keep buffer size limited
                    if len(self._frame_buffer) > self.config.buffer_size:
                        self._frame_buffer.pop(0)
            
            # Sleep to maintain FPS
            elapsed = time.time() - start_time
            sleep_time = max(0, frame_interval - elapsed)
            if sleep_time > 0:
                time.sleep(sleep_time)
    
    async def _async_capture_loop(self) -> None:
        """Continuous capture loop for async mode."""
        frame_interval = 1.0 / self.config.fps
        
        while self._running:
            start_time = time.time()
            
            # Capture frame
            frame_data = self.capture_screen()
            if frame_data:
                # Add to buffer
                with self._buffer_lock:
                    self._frame_buffer.append(frame_data)
                    # Keep buffer size limited
                    if len(self._frame_buffer) > self.config.buffer_size:
                        self._frame_buffer.pop(0)
            
            # Sleep to maintain FPS
            elapsed = time.time() - start_time
            sleep_time = max(0, frame_interval - elapsed)
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
    
    def get_latest_frame(self) -> Optional[bytes]:
        """Get the latest captured frame from buffer."""
        with self._buffer_lock:
            if self._frame_buffer:
                return self._frame_buffer[-1]
        return None
    
    def get_all_frames(self) -> List[bytes]:
        """Get all frames from buffer and clear it."""
        with self._buffer_lock:
            frames = self._frame_buffer.copy()
            self._frame_buffer.clear()
        return frames
    
    def stop_capture(self) -> None:
        """Stop continuous screen capture."""
        if not self._running:
            return
        
        self._running = False
        
        if self._capture_thread and self._capture_thread.is_alive():
            self._capture_thread.join(timeout=2.0)
        
        logger.info("Screen capture stopped", stats=self.stats.__dict__)
    
    def get_stats(self) -> CaptureStats:
        """Get capture statistics."""
        return self.stats
    
    def reset_stats(self) -> None:
        """Reset capture statistics."""
        self.stats = CaptureStats()
    
    def __del__(self):
        """Cleanup on destruction."""
        self.stop_capture()
        if hasattr(self, 'sct'):
            self.sct.close()
