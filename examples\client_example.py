#!/usr/bin/env python3
"""
Example HTTP client for Streamable HTTP MCP server.
Demonstrates how to connect and interact with the new HTTP-based streaming server.
"""

import asyncio
import json
import base64
import time
from typing import Optional, Dict, Any
import aiohttp
import aiofiles


class StreamableHTTPClient:
    """Example client for Streamable HTTP MCP server."""

    def __init__(self, base_url: str = "http://localhost:8080"):
        """Initialize client with server base URL."""
        self.base_url = base_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        self.client_id: Optional[str] = None
        self.running = False
        self.frame_count = 0
        self.start_time = 0.0
    
    async def connect(self):
        """Initialize HTTP session and test connection."""
        try:
            print(f"Connecting to {self.base_url}...")
            self.session = aiohttp.ClientSession()

            # Test connection with health check
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    health_data = await response.json()
                    print("Connected successfully!")
                    print(f"Server status: {health_data.get('status')}")
                    print(f"Transport: {health_data.get('transport_state')}")
                    return True
                else:
                    print(f"Health check failed: {response.status}")
                    return False

        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    async def disconnect(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
            print("Disconnected from server")

    async def send_mcp_message(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send MCP JSON-RPC message to server."""
        if not self.session:
            print("Not connected to server")
            return {}

        message = {
            "jsonrpc": "2.0",
            "id": int(time.time() * 1000),
            "method": method,
            "params": params or {}
        }

        try:
            async with self.session.post(
                f"{self.base_url}/mcp/message",
                json=message,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    print(f"MCP request failed: {response.status}")
                    return {}
        except Exception as e:
            print(f"Failed to send MCP message: {e}")
            return {}
    
    async def start_stream(self, fps: int = 30, quality: str = "adaptive"):
        """Start screen streaming using MCP tools."""
        params = {
            "fps": fps,
            "quality": quality
        }
        result = await self.send_mcp_message("tools/call", {
            "name": "start_stream",
            "arguments": params
        })
        print(f"Stream start result: {result}")
        return result

    async def stop_stream(self):
        """Stop screen streaming using MCP tools."""
        result = await self.send_mcp_message("tools/call", {
            "name": "stop_stream",
            "arguments": {}
        })
        print(f"Stream stop result: {result}")
        return result

    async def capture_screenshot(self):
        """Capture a single screenshot using MCP tools."""
        result = await self.send_mcp_message("tools/call", {
            "name": "capture_screenshot",
            "arguments": {}
        })
        print(f"Screenshot result: {result}")
        return result

    async def get_stream_status(self):
        """Get stream status using MCP tools."""
        result = await self.send_mcp_message("tools/call", {
            "name": "get_stream_status",
            "arguments": {}
        })
        print(f"Stream status: {result}")
        return result

    async def list_tools(self):
        """List available MCP tools."""
        result = await self.send_mcp_message("tools/list")
        print(f"Available tools: {result}")
        return result
    
    async def stream_screen_data(self, duration: int = 10):
        """Stream screen data from HTTP endpoint."""
        if not self.session:
            print("Not connected to server")
            return

        self.running = True
        self.start_time = time.time()
        self.frame_count = 0

        print(f"Starting screen stream for {duration} seconds...")

        try:
            async with self.session.get(f"{self.base_url}/stream/screen") as response:
                if response.status != 200:
                    print(f"Stream request failed: {response.status}")
                    return

                print("Connected to screen stream")

                # Read chunked data
                async for chunk in response.content.iter_chunked(8192):
                    if not self.running:
                        break

                    if time.time() - self.start_time > duration:
                        print(f"Stream duration ({duration}s) reached")
                        break

                    await self.handle_stream_chunk(chunk)

        except Exception as e:
            print(f"Error in screen stream: {e}")
        finally:
            self.running = False
            print("Screen stream ended")
    
    async def handle_stream_chunk(self, chunk: bytes):
        """Handle HTTP stream chunk data."""
        try:
            # Parse HTTP chunk format
            chunk_str = chunk.decode('utf-8', errors='ignore')

            # Look for chunk size and data
            if '\r\n' in chunk_str:
                lines = chunk_str.split('\r\n')
                for line in lines:
                    if ':' in line and line.count(':') >= 2:
                        # This might be our frame data
                        try:
                            parts = line.split(':', 2)
                            if len(parts) >= 2:
                                # Try to decode metadata and data
                                metadata_b64 = parts[0]
                                data_b64 = parts[1]

                                # Decode metadata
                                metadata_str = base64.b64decode(metadata_b64).decode()
                                metadata = eval(metadata_str)  # Simple eval for demo

                                await self.handle_frame_data(metadata, data_b64)

                        except Exception as e:
                            # Not our format, skip
                            pass

        except Exception as e:
            print(f"Error parsing chunk: {e}")
    
    async def handle_frame_data(self, metadata: dict, data_b64: str):
        """Handle frame data from HTTP stream."""
        self.frame_count += 1
        frame_id = metadata.get("frame_id")
        format_type = metadata.get("format")
        compression = metadata.get("compression")
        size = metadata.get("size")
        quality = metadata.get("quality")

        # Calculate FPS
        elapsed = time.time() - self.start_time
        fps = self.frame_count / elapsed if elapsed > 0 else 0

        print(f"Frame {frame_id}: {format_type}, {compression}, "
              f"quality: {quality}, size: {size}, FPS: {fps:.1f}")

        # Optionally save frame to file
        if self.frame_count <= 3:  # Save first 3 frames as example
            await self.save_frame_data(data_b64, frame_id, format_type)
    
    async def handle_single_frame(self, data: dict):
        """Handle single frame data."""
        format_type = data.get("format")
        size = data.get("size")
        timestamp = data.get("timestamp")
        
        print(f"Single frame received: {format_type}, {size} bytes, timestamp: {timestamp}")
        
        # Save the frame
        await self.save_frame(data, "single")
    
    async def handle_status(self, data: dict):
        """Handle status response."""
        stream_info = data.get("stream_info", {})
        active_streams = data.get("active_streams")
        connections = data.get("connections")
        
        print(f"Server Status:")
        print(f"  Active streams: {active_streams}")
        print(f"  Total connections: {connections}")
        print(f"  Stream state: {stream_info.get('state')}")
        
        if "stats" in stream_info:
            stats = stream_info["stats"]
            print(f"  Total frames: {stats.get('total_frames')}")
            print(f"  Average FPS: {stats.get('avg_fps', 0):.1f}")
            print(f"  Compression ratio: {stats.get('avg_compression_ratio', 1.0):.2f}")
    
    async def save_frame_data(self, data_b64: str, frame_id: int, format_type: str):
        """Save frame data to file."""
        try:
            if data_b64:
                # Decode base64 data
                image_data = base64.b64decode(data_b64)

                # Save to file
                filename = f"frame_{frame_id}.{format_type}"
                async with aiofiles.open(filename, "wb") as f:
                    await f.write(image_data)

                print(f"Saved frame to {filename} ({len(image_data)} bytes)")

        except Exception as e:
            print(f"Failed to save frame: {e}")

    def stop_streaming(self):
        """Stop streaming."""
        self.running = False


async def main():
    """Main example function."""
    client = StreamableHTTPClient()

    # Connect to server
    if not await client.connect():
        return

    try:
        # Example 1: List available tools
        print("\n=== Listing available MCP tools ===")
        await client.list_tools()
        await asyncio.sleep(1)

        # Example 2: Get stream status
        print("\n=== Getting stream status ===")
        await client.get_stream_status()
        await asyncio.sleep(1)

        # Example 3: Capture single screenshot
        print("\n=== Capturing screenshot ===")
        await client.capture_screenshot()
        await asyncio.sleep(2)

        # Example 4: Start streaming
        print("\n=== Starting stream ===")
        await client.start_stream(fps=10, quality="medium")
        await asyncio.sleep(1)

        # Example 5: Stream screen data for 10 seconds
        print("\n=== Streaming screen data for 10 seconds ===")
        stream_task = asyncio.create_task(client.stream_screen_data(duration=10))
        await stream_task

        # Example 6: Stop streaming
        print("\n=== Stopping stream ===")
        await client.stop_stream()
        await asyncio.sleep(1)

    except KeyboardInterrupt:
        print("\nInterrupted by user")
        client.stop_streaming()
    except Exception as e:
        print(f"Error in main: {e}")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    print("Streamable HTTP MCP Client Example")
    print("==================================")
    asyncio.run(main())
