"""
Configuration module for base64_streaming_mcp server.
Handles all configuration settings with cross-platform support.
"""

import os
import platform
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class CompressionType(str, Enum):
    """Supported compression types."""
    NONE = "none"
    GZIP = "gzip"
    ZSTD = "zstd"


class ImageFormat(str, Enum):
    """Supported image formats."""
    PNG = "png"
    JPEG = "jpeg"
    WEBP = "webp"


class MCPServerConfig(BaseModel):
    """Main configuration for MCP server."""
    
    # Server settings
    host: str = Field(default="localhost", description="Server host address")
    port: int = Field(default=8080, description="HTTP server port")
    websocket_port: int = Field(default=7788, description="Legacy WebSocket server port")
    max_connections: int = Field(default=10, description="Maximum concurrent connections")

    # Transport settings
    transport_type: str = Field(default="streamable_http", description="Transport type (streamable_http, websocket)")
    http_version: str = Field(default="1.1", description="HTTP version (1.1, 2.0)")
    enable_compression: bool = Field(default=True, description="Enable HTTP compression")
    chunk_size: int = Field(default=64 * 1024, description="HTTP chunk size in bytes")
    
    # Screen capture settings
    fps: int = Field(default=30, description="Frames per second for screen capture", ge=1, le=60)
    monitor_index: int = Field(default=0, description="Monitor index to capture (0 = primary)")
    capture_region: Optional[Dict[str, int]] = Field(
        default=None, 
        description="Capture region {x, y, width, height}. None = full screen"
    )
    
    # Image processing settings
    image_format: ImageFormat = Field(default=ImageFormat.JPEG, description="Output image format")
    jpeg_quality: int = Field(default=85, description="JPEG quality (1-100)", ge=1, le=100)
    max_width: Optional[int] = Field(default=1920, description="Maximum image width")
    max_height: Optional[int] = Field(default=1080, description="Maximum image height")
    
    # Streaming settings
    compression: CompressionType = Field(default=CompressionType.GZIP, description="Compression type")
    chunk_size: int = Field(default=64 * 1024, description="Chunk size for streaming (bytes)")
    buffer_size: int = Field(default=10, description="Frame buffer size")
    
    # Performance settings
    enable_threading: bool = Field(default=True, description="Enable multi-threading")
    memory_limit_mb: int = Field(default=512, description="Memory limit in MB")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    
    # MCP specific settings
    server_name: str = Field(default="base64-streaming-mcp", description="MCP server name")
    server_version: str = Field(default="1.0.0", description="MCP server version")
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "MCP_"
        case_sensitive = False


def get_default_config() -> MCPServerConfig:
    """Get default configuration with platform-specific optimizations."""
    config = MCPServerConfig()
    
    # Platform-specific optimizations
    system = platform.system().lower()
    
    if system == "windows":
        # Windows optimizations
        config.enable_threading = True
        config.chunk_size = 128 * 1024  # Larger chunks on Windows
        
    elif system == "darwin":  # macOS
        # macOS optimizations
        config.fps = 25  # Slightly lower FPS for better performance
        config.compression = CompressionType.ZSTD
        
    elif system == "linux":
        # Linux optimizations
        config.enable_threading = True
        config.compression = CompressionType.GZIP
    
    return config


def load_config_from_env() -> MCPServerConfig:
    """Load configuration from environment variables."""
    return MCPServerConfig()


def load_config_from_file(file_path: str) -> MCPServerConfig:
    """Load configuration from JSON file."""
    import json
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Config file not found: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    return MCPServerConfig(**config_data)


def save_config_to_file(config: MCPServerConfig, file_path: str) -> None:
    """Save configuration to JSON file."""
    import json
    
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(config.dict(), f, indent=2, ensure_ascii=False)


# Global configuration instance
_config: Optional[MCPServerConfig] = None


def get_config() -> MCPServerConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = get_default_config()
    return _config


def set_config(config: MCPServerConfig) -> None:
    """Set the global configuration instance."""
    global _config
    _config = config


def reset_config() -> None:
    """Reset configuration to defaults."""
    global _config
    _config = get_default_config()


# Platform detection utilities
def is_windows() -> bool:
    """Check if running on Windows."""
    return platform.system().lower() == "windows"


def is_macos() -> bool:
    """Check if running on macOS."""
    return platform.system().lower() == "darwin"


def is_linux() -> bool:
    """Check if running on Linux."""
    return platform.system().lower() == "linux"


def get_platform_info() -> Dict[str, Any]:
    """Get detailed platform information."""
    return {
        "system": platform.system(),
        "release": platform.release(),
        "version": platform.version(),
        "machine": platform.machine(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
    }
