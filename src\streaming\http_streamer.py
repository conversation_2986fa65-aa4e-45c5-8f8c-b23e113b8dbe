"""
HTTP Streaming Module for Screen Capture

This module provides optimized HTTP streaming for screen capture data using
chunked transfer encoding and advanced compression techniques.

Features:
- HTTP chunked transfer encoding
- Real-time frame streaming
- Adaptive quality control
- Compression optimization
- Frame rate adaptation
- Buffer management
"""

import asyncio
import base64
import gzip
import time
import zstandard as zstd
from typing import Optional, AsyncGenerator, Dict, Any, List, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import io

import structlog
from PIL import Image

from ..config import MCPServerConfig, CompressionType, ImageFormat
from ..screen_capture import ScreenCapture

logger = structlog.get_logger(__name__)


class StreamQuality(str, Enum):
    """Stream quality levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    ADAPTIVE = "adaptive"


@dataclass
class HTTPStreamFrame:
    """HTTP stream frame data structure."""
    frame_id: int
    timestamp: float
    data: bytes
    format: str
    size: Tuple[int, int]
    quality: int
    compression: Optional[str] = None
    chunk_size: int = 0
    
    def to_http_chunk(self) -> bytes:
        """Convert frame to HTTP chunk format."""
        # Create frame metadata
        metadata = {
            "frame_id": self.frame_id,
            "timestamp": self.timestamp,
            "format": self.format,
            "size": self.size,
            "quality": self.quality,
            "compression": self.compression
        }
        
        # Create chunk with metadata header
        metadata_json = base64.b64encode(
            str(metadata).encode()
        ).decode()
        
        # Combine metadata and data
        chunk_data = f"{metadata_json}:{base64.b64encode(self.data).decode()}"
        chunk_bytes = chunk_data.encode()
        
        # HTTP chunk format: size in hex + CRLF + data + CRLF
        chunk_size_hex = f"{len(chunk_bytes):x}"
        return f"{chunk_size_hex}\r\n".encode() + chunk_bytes + b"\r\n"


@dataclass
class StreamingStats:
    """HTTP streaming statistics."""
    frames_sent: int = 0
    bytes_sent: int = 0
    chunks_sent: int = 0
    compression_ratio: float = 0.0
    avg_frame_size: float = 0.0
    current_fps: float = 0.0
    target_fps: float = 30.0
    quality_adjustments: int = 0
    errors: int = 0
    start_time: float = 0.0
    last_frame_time: float = 0.0
    
    def update_frame_stats(self, frame_size: int, compressed_size: int):
        """Update frame statistics."""
        self.frames_sent += 1
        self.bytes_sent += compressed_size
        self.chunks_sent += 1
        
        # Update compression ratio
        if frame_size > 0:
            self.compression_ratio = compressed_size / frame_size
        
        # Update average frame size
        self.avg_frame_size = self.bytes_sent / self.frames_sent if self.frames_sent > 0 else 0
        
        # Update FPS
        current_time = time.time()
        if self.last_frame_time > 0:
            frame_interval = current_time - self.last_frame_time
            if frame_interval > 0:
                self.current_fps = 1.0 / frame_interval
        self.last_frame_time = current_time


class HTTPScreenStreamer:
    """HTTP-optimized screen capture streamer."""
    
    def __init__(self, config: Optional[MCPServerConfig] = None):
        """Initialize HTTP screen streamer."""
        self.config = config or MCPServerConfig()
        self.screen_capture = ScreenCapture(self.config)
        self.stats = StreamingStats()
        
        # Streaming state
        self.is_streaming = False
        self.stream_quality = StreamQuality.ADAPTIVE
        self.current_quality = self.config.jpeg_quality
        
        # Compression setup
        self._setup_compression()
        
        # Adaptive quality parameters
        self.quality_min = 30
        self.quality_max = 95
        self.target_chunk_size = 64 * 1024  # 64KB target chunk size
        self.max_chunk_size = 256 * 1024   # 256KB max chunk size
        
        # Frame management
        self.frame_counter = 0
        self.frame_buffer: List[HTTPStreamFrame] = []
        self.max_buffer_size = 10
        
        logger.info(
            "HTTP screen streamer initialized",
            quality=self.stream_quality.value,
            compression=self.config.compression.value,
            target_fps=self.config.fps
        )
    
    def _setup_compression(self):
        """Setup compression based on configuration."""
        if self.config.compression == CompressionType.ZSTD:
            self.compressor = zstd.ZstdCompressor(level=3)
        elif self.config.compression == CompressionType.GZIP:
            self.compressor = None  # Use gzip module
        else:
            self.compressor = None
    
    def _compress_data(self, data: bytes) -> Tuple[bytes, str]:
        """Compress frame data."""
        if self.config.compression == CompressionType.NONE:
            return data, "none"
        
        try:
            if self.config.compression == CompressionType.ZSTD and self.compressor:
                compressed = self.compressor.compress(data)
                return compressed, "zstd"
            elif self.config.compression == CompressionType.GZIP:
                compressed = gzip.compress(data, compresslevel=6)
                return compressed, "gzip"
            else:
                return data, "none"
        except Exception as e:
            logger.error("Compression failed", error=str(e))
            return data, "none"
    
    def _adjust_quality(self, frame_size: int) -> int:
        """Adjust quality based on frame size and performance."""
        if self.stream_quality != StreamQuality.ADAPTIVE:
            return self.current_quality
        
        # Adjust quality based on frame size
        if frame_size > self.max_chunk_size:
            # Frame too large, reduce quality
            self.current_quality = max(self.quality_min, self.current_quality - 5)
            self.stats.quality_adjustments += 1
        elif frame_size < self.target_chunk_size // 2:
            # Frame too small, increase quality
            self.current_quality = min(self.quality_max, self.current_quality + 2)
            self.stats.quality_adjustments += 1
        
        return self.current_quality
    
    def _capture_and_encode_frame(self) -> Optional[HTTPStreamFrame]:
        """Capture and encode a single frame."""
        try:
            # Capture screen
            frame_data = self.screen_capture.capture_screen()
            if not frame_data:
                return None
            
            # Convert to PIL Image for processing
            img = Image.open(io.BytesIO(frame_data))
            
            # Adjust quality if needed
            quality = self._adjust_quality(len(frame_data))
            
            # Re-encode with adjusted quality if needed
            if quality != self.config.jpeg_quality or self.config.image_format != ImageFormat.JPEG:
                output = io.BytesIO()
                
                if self.config.image_format == ImageFormat.JPEG:
                    img.save(output, format='JPEG', quality=quality, optimize=True)
                elif self.config.image_format == ImageFormat.PNG:
                    img.save(output, format='PNG', optimize=True)
                elif self.config.image_format == ImageFormat.WEBP:
                    img.save(output, format='WEBP', quality=quality, optimize=True)
                
                frame_data = output.getvalue()
            
            # Compress frame data
            compressed_data, compression_type = self._compress_data(frame_data)
            
            # Create frame object
            frame = HTTPStreamFrame(
                frame_id=self.frame_counter,
                timestamp=time.time(),
                data=compressed_data,
                format=self.config.image_format.value,
                size=img.size,
                quality=quality,
                compression=compression_type,
                chunk_size=len(compressed_data)
            )
            
            # Update statistics
            self.stats.update_frame_stats(len(frame_data), len(compressed_data))
            
            self.frame_counter += 1
            return frame
            
        except Exception as e:
            self.stats.errors += 1
            logger.error("Frame capture/encoding failed", error=str(e), exc_info=True)
            return None
    
    async def start_streaming(self) -> AsyncGenerator[bytes, None]:
        """Start HTTP streaming of screen capture."""
        if self.is_streaming:
            logger.warning("Streaming already active")
            return
        
        self.is_streaming = True
        self.stats.start_time = time.time()
        self.stats.target_fps = self.config.fps
        
        logger.info("Starting HTTP screen streaming", fps=self.config.fps)
        
        frame_interval = 1.0 / self.config.fps
        last_frame_time = 0.0
        
        try:
            while self.is_streaming:
                current_time = time.time()
                
                # Check if it's time for next frame
                if current_time - last_frame_time >= frame_interval:
                    # Capture and encode frame
                    frame = self._capture_and_encode_frame()
                    
                    if frame:
                        # Convert to HTTP chunk
                        chunk_data = frame.to_http_chunk()
                        
                        # Add to buffer
                        self.frame_buffer.append(frame)
                        if len(self.frame_buffer) > self.max_buffer_size:
                            self.frame_buffer.pop(0)
                        
                        # Yield chunk
                        yield chunk_data
                        
                        last_frame_time = current_time
                
                # Small sleep to prevent busy waiting
                await asyncio.sleep(0.001)
                
        except Exception as e:
            self.stats.errors += 1
            logger.error("Streaming error", error=str(e), exc_info=True)
        finally:
            self.is_streaming = False
            logger.info("HTTP screen streaming stopped")
    
    def stop_streaming(self):
        """Stop HTTP streaming."""
        self.is_streaming = False
        logger.info("Stopping HTTP screen streaming")
    
    def set_quality(self, quality: StreamQuality):
        """Set streaming quality."""
        self.stream_quality = quality
        
        if quality == StreamQuality.LOW:
            self.current_quality = 40
            self.target_chunk_size = 32 * 1024
        elif quality == StreamQuality.MEDIUM:
            self.current_quality = 70
            self.target_chunk_size = 64 * 1024
        elif quality == StreamQuality.HIGH:
            self.current_quality = 90
            self.target_chunk_size = 128 * 1024
        # ADAPTIVE uses dynamic adjustment
        
        logger.info("Stream quality changed", quality=quality.value, current_quality=self.current_quality)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get streaming statistics."""
        return {
            "streaming": self.is_streaming,
            "quality": self.stream_quality.value,
            "current_quality": self.current_quality,
            "stats": asdict(self.stats),
            "buffer_size": len(self.frame_buffer),
            "frame_counter": self.frame_counter
        }
    
    def get_latest_frame(self) -> Optional[HTTPStreamFrame]:
        """Get the latest frame from buffer."""
        return self.frame_buffer[-1] if self.frame_buffer else None
    
    async def get_single_frame_chunk(self) -> Optional[bytes]:
        """Get a single frame as HTTP chunk."""
        frame = self._capture_and_encode_frame()
        if frame:
            return frame.to_http_chunk()
        return None
