"""
Tests for Streamable HTTP MCP Server

Basic tests to validate the new Streamable HTTP architecture.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch

from src.config import MCPServerConfig
from src.transport import StreamableHTTPTransport, HTTPStreamMessage
from src.protocol import StreamableHTTP<PERSON>PAdapter
from src.streaming import HTTPScreenStreamer, StreamQuality
from src.streamable_http_server import StreamableHTTPMCPServer


class TestStreamableHTTPTransport:
    """Test Streamable HTTP transport layer."""
    
    def test_transport_initialization(self):
        """Test transport initialization."""
        transport = StreamableHTTPTransport("localhost", 8080)
        
        assert transport.host == "localhost"
        assert transport.port == 8080
        assert transport.state.value == "disconnected"
        assert len(transport.active_connections) == 0
    
    def test_http_stream_message(self):
        """Test HTTP stream message creation."""
        message = HTTPStreamMessage(
            id="test-123",
            type="test_message",
            data={"key": "value"},
            timestamp=1234567890.0
        )
        
        assert message.id == "test-123"
        assert message.type == "test_message"
        assert message.data == {"key": "value"}
        
        # Test JSON serialization
        json_str = message.to_json()
        parsed = json.loads(json_str)
        assert parsed["id"] == "test-123"
        assert parsed["type"] == "test_message"
        
        # Test deserialization
        reconstructed = HTTPStreamMessage.from_json(json_str)
        assert reconstructed.id == message.id
        assert reconstructed.type == message.type


class TestMCPAdapter:
    """Test MCP protocol adapter."""
    
    def test_adapter_initialization(self):
        """Test adapter initialization."""
        config = MCPServerConfig()
        transport = StreamableHTTPTransport()
        adapter = StreamableHTTPMCPAdapter(config, transport)
        
        assert adapter.config == config
        assert adapter.transport == transport
        assert not adapter.initialized
        assert adapter.capabilities.tools
        assert adapter.capabilities.resources
        assert adapter.capabilities.streaming
    
    @pytest.mark.asyncio
    async def test_initialize_handler(self):
        """Test MCP initialization handler."""
        config = MCPServerConfig()
        transport = StreamableHTTPTransport()
        adapter = StreamableHTTPMCPAdapter(config, transport)
        
        # Test initialization message
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {"listChanged": True}
                }
            }
        }
        
        result = await adapter._handle_initialize(init_message)
        
        assert adapter.initialized
        assert "protocolVersion" in result
        assert "capabilities" in result
        assert "serverInfo" in result
        assert result["serverInfo"]["name"] == config.server_name


class TestHTTPScreenStreamer:
    """Test HTTP screen streamer."""
    
    def test_streamer_initialization(self):
        """Test streamer initialization."""
        config = MCPServerConfig()
        streamer = HTTPScreenStreamer(config)
        
        assert streamer.config == config
        assert not streamer.is_streaming
        assert streamer.stream_quality == StreamQuality.ADAPTIVE
        assert streamer.frame_counter == 0
    
    def test_quality_adjustment(self):
        """Test adaptive quality adjustment."""
        config = MCPServerConfig()
        streamer = HTTPScreenStreamer(config)
        
        # Test large frame size (should reduce quality)
        large_frame_size = 300 * 1024  # 300KB
        initial_quality = streamer.current_quality
        adjusted_quality = streamer._adjust_quality(large_frame_size)
        
        assert adjusted_quality <= initial_quality
        
        # Test small frame size (should increase quality)
        small_frame_size = 10 * 1024  # 10KB
        adjusted_quality = streamer._adjust_quality(small_frame_size)
        
        # Quality should increase from the reduced level
        assert adjusted_quality >= streamer.current_quality
    
    def test_set_quality(self):
        """Test quality setting."""
        config = MCPServerConfig()
        streamer = HTTPScreenStreamer(config)
        
        # Test different quality levels
        streamer.set_quality(StreamQuality.LOW)
        assert streamer.stream_quality == StreamQuality.LOW
        assert streamer.current_quality == 40
        
        streamer.set_quality(StreamQuality.HIGH)
        assert streamer.stream_quality == StreamQuality.HIGH
        assert streamer.current_quality == 90


class TestStreamableHTTPMCPServer:
    """Test main Streamable HTTP MCP server."""
    
    def test_server_initialization(self):
        """Test server initialization."""
        config = MCPServerConfig()
        server = StreamableHTTPMCPServer(config)
        
        assert server.config == config
        assert not server.running
        assert server.start_time is None
        
        # Check components are initialized
        assert server.transport is not None
        assert server.adapter is not None
        assert server.streamer is not None
        assert server.screen_capture is not None
    
    @pytest.mark.asyncio
    async def test_tool_handlers(self):
        """Test MCP tool handlers."""
        config = MCPServerConfig()
        server = StreamableHTTPMCPServer(config)
        
        # Test start stream tool
        result = await server._tool_start_stream({
            "quality": "high",
            "fps": 30
        })
        
        assert result["status"] == "started"
        assert result["quality"] == "high"
        assert result["fps"] == 30
        
        # Test stop stream tool
        result = await server._tool_stop_stream({})
        assert result["status"] == "stopped"
        
        # Test get stream status
        status = await server._tool_get_stream_status({})
        assert "streaming" in status
        assert "stats" in status
    
    @pytest.mark.asyncio
    async def test_resource_handlers(self):
        """Test MCP resource handlers."""
        config = MCPServerConfig()
        server = StreamableHTTPMCPServer(config)
        
        # Mock screen capture to avoid actual screen capture
        with patch.object(server.screen_capture, 'capture_screen') as mock_capture:
            mock_capture.return_value = b"fake_image_data"
            
            # Test current screen resource
            result = await server._resource_current_screen()
            assert result == b"fake_image_data"
            mock_capture.assert_called_once()
        
        # Test stream status resource
        status = await server._resource_stream_status()
        assert isinstance(status, dict)
        assert "streaming" in status
        
        # Test monitors info resource
        monitors = await server._resource_monitors_info()
        assert isinstance(monitors, dict)
        assert "monitors" in monitors
    
    def test_server_info(self):
        """Test server info generation."""
        config = MCPServerConfig()
        server = StreamableHTTPMCPServer(config)
        
        info = server.get_server_info()
        
        assert info["name"] == config.server_name
        assert info["version"] == config.server_version
        assert info["transport"] == "streamable_http"
        assert not info["running"]
        assert "endpoints" in info
        assert "config" in info
        
        # Check endpoints
        endpoints = info["endpoints"]
        assert "mcp_message" in endpoints
        assert "mcp_stream" in endpoints
        assert "screen_stream" in endpoints
        assert "health" in endpoints


@pytest.mark.asyncio
async def test_integration():
    """Integration test for the complete system."""
    config = MCPServerConfig()
    config.port = 8081  # Use different port for testing
    
    server = StreamableHTTPMCPServer(config)
    
    # Test server info before starting
    info = server.get_server_info()
    assert not info["running"]
    
    # Mock the actual server start to avoid binding to ports
    with patch.object(server.transport, 'start') as mock_start:
        mock_start.return_value = None
        
        await server.start()
        
        # Check server state
        assert server.running
        assert server.start_time is not None
        mock_start.assert_called_once()
        
        # Test server info after starting
        info = server.get_server_info()
        assert info["running"]
        assert info["uptime"] >= 0
        
        # Stop server
        with patch.object(server.transport, 'stop') as mock_stop:
            mock_stop.return_value = None
            
            await server.stop()
            
            assert not server.running
            mock_stop.assert_called_once()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
