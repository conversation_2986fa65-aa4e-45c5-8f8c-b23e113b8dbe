"""
Main entry point for Streamable HTTP MCP Server

This script starts the Streamable HTTP MCP Server with proper configuration
and signal handling for graceful shutdown.
"""

import asyncio
import signal
import sys
import argparse
from pathlib import Path
from typing import Optional

import structlog

from .config import get_config, load_config_from_file, MCPServerConfig
from .streamable_http_server import StreamableHTTPMCPServer

# Setup logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class ServerManager:
    """Manages the Streamable HTTP MCP Server lifecycle."""
    
    def __init__(self, config: MCPServerConfig):
        """Initialize server manager."""
        self.config = config
        self.server: Optional[StreamableHTTPMCPServer] = None
        self.shutdown_event = asyncio.Event()
    
    async def start_server(self):
        """Start the MCP server."""
        try:
            logger.info("Initializing Streamable HTTP MCP Server...")
            
            self.server = StreamableHTTPMCPServer(self.config)
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Start the server
            await self.server.start()
            
            logger.info(
                "Server started successfully",
                server_info=self.server.get_server_info()
            )
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except Exception as e:
            logger.error("Failed to start server", error=str(e), exc_info=True)
            raise
        finally:
            await self.stop_server()
    
    async def stop_server(self):
        """Stop the MCP server."""
        if self.server:
            logger.info("Shutting down server...")
            try:
                await self.server.stop()
                logger.info("Server shutdown complete")
            except Exception as e:
                logger.error("Error during server shutdown", error=str(e))
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Streamable HTTP MCP Server for real-time screen capture streaming"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        help="Path to configuration file (JSON format)"
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Server host address (default: localhost)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="Server port (default: 8080)"
    )
    
    parser.add_argument(
        "--fps",
        type=int,
        default=30,
        help="Frames per second for screen capture (default: 30)"
    )
    
    parser.add_argument(
        "--quality",
        type=int,
        default=85,
        help="JPEG quality (1-100, default: 85)"
    )
    
    parser.add_argument(
        "--compression",
        type=str,
        choices=["none", "gzip", "zstd"],
        default="gzip",
        help="Compression type (default: gzip)"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Streamable HTTP MCP Server 1.0.0"
    )
    
    return parser.parse_args()


def load_configuration(args) -> MCPServerConfig:
    """Load configuration from file or command line arguments."""
    if args.config:
        config_path = Path(args.config)
        if config_path.exists():
            logger.info("Loading configuration from file", path=str(config_path))
            config = load_config_from_file(str(config_path))
        else:
            logger.error("Configuration file not found", path=str(config_path))
            sys.exit(1)
    else:
        logger.info("Using default configuration")
        config = get_config()
    
    # Override with command line arguments
    if args.host != "localhost":
        config.host = args.host
    
    if args.port != 8080:
        config.port = args.port
    
    if args.fps != 30:
        config.fps = args.fps
    
    if args.quality != 85:
        config.jpeg_quality = args.quality
    
    if args.compression != "gzip":
        from .config import CompressionType
        config.compression = CompressionType(args.compression)
    
    if args.log_level != "INFO":
        config.log_level = args.log_level
    
    return config


def print_startup_banner(config: MCPServerConfig):
    """Print startup banner with server information."""
    banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                     Streamable HTTP MCP Server v1.0.0                       ║
║                                                                              ║
║  🚀 Real-time Screen Capture Streaming via Streamable HTTP Protocol         ║
║                                                                              ║
║  Server Configuration:                                                       ║
║    • Host: {config.host:<20} Port: {config.port:<10}                      ║
║    • Transport: Streamable HTTP      FPS: {config.fps:<10}                 ║
║    • Compression: {config.compression.value:<15} Quality: {config.jpeg_quality:<10}           ║
║                                                                              ║
║  Endpoints:                                                                  ║
║    • MCP Messages:  http://{config.host}:{config.port}/mcp/message                    ║
║    • MCP Stream:    http://{config.host}:{config.port}/mcp/stream                     ║
║    • Screen Stream: http://{config.host}:{config.port}/stream/screen                  ║
║    • Health Check:  http://{config.host}:{config.port}/health                         ║
║                                                                              ║
║  Press Ctrl+C to stop the server                                            ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)


async def main():
    """Main entry point."""
    try:
        # Parse arguments
        args = parse_arguments()
        
        # Load configuration
        config = load_configuration(args)
        
        # Print startup banner
        print_startup_banner(config)
        
        # Create and start server manager
        manager = ServerManager(config)
        await manager.start_server()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error("Unexpected error", error=str(e), exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # Run the server
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
