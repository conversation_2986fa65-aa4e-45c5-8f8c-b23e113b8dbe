#!/usr/bin/env python3
"""
Simple script to run the Streamable HTTP MCP Server
"""

import sys
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import from src package
from src.main import main

if __name__ == "__main__":
    print("🚀 Starting Streamable HTTP MCP Server...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n✅ Server stopped by user.")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
